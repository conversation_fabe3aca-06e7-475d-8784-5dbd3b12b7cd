# Search Functionality Fixes

## Overview

This document outlines the comprehensive fixes applied to the site search functionality to resolve URL encoding issues, 403 errors, search toggle button problems, and ensure consistent search behavior across both the main CakePHP site and WordPress blog.

## Issues Identified and Resolved

### 1. URL Encoding Problems with Spaces

**Problem**: Search terms containing spaces were being encoded as `%20` which caused 403 errors on the server.

**Root Cause**: Default `encodeURIComponent()` function encodes spaces as `%20`, but the server configuration was rejecting URLs with `%20` encoding.

**Solution**: Modified search form handlers to replace `%20` with `+` encoding for spaces.

### 2. Search Toggle Button Conflicts

**Problem**: Search toggle buttons containing images weren't working properly due to event handling conflicts between the main navigation system and search functionality.

**Root Cause**: The `App.Navigation` system was interfering with search toggle functionality when buttons contained child elements (images).

**Solution**: Created a dedicated search toggle fix script that removes conflicting event handlers and implements proper toggle behavior.

### 3. Inconsistent Search Forms

**Problem**: Multiple search forms across the site (header, sidebar, search results page) had different behaviors and URL generation patterns.

**Root Cause**: Different form implementations without centralized handling logic.

**Solution**: Standardized all search forms to use the same URL generation and encoding logic.

## Technical Implementation

### 1. Search URL Routing

The search system uses clean URLs with the following routing configuration:

```php
// app/config/routes.php
Router::connect('/search/:term/*', array('plugin' => 'site_search', 'controller' => 'search', 'action' => 'results'));
Router::connect('/search', array('plugin' => 'site_search', 'controller' => 'search', 'action' => 'results'));
```

**URL Format**: `/search/search-term` (preferred) or `/search?search=search-term` (fallback)

### 2. Search Controller Enhancements

**File**: `app/plugins/site_search/controllers/search_controller.php`

Key improvements:
- **Dual Parameter Handling**: Accepts search terms from both URL parameters and GET parameters
- **Space Encoding Support**: Handles both `+` and `%20` encoding for spaces
- **Auth Component Fix**: Ensures proper authentication handling for public search access

```php
// Get search term from either URL parameter or GET parameter
if (isset($this->params['term'])) {
  // Handle both + and %20 encoding for spaces
  $term = str_replace(array('+', '%20'), ' ', $this->params['term']);
} elseif (!empty($_GET['search'])) {
  $term = $_GET['search'];
}
```

### 3. Search Form Standardization

**Primary Search Element**: `app/views/elements/chrome/search.ctp`

**Key Features**:
- **Event Handler Isolation**: Clones form elements to remove conflicting event listeners
- **Priority Event Handling**: Uses capture phase for higher priority event handling
- **Clean URL Generation**: Converts form submissions to clean URLs with proper encoding

```javascript
// Use + for spaces instead of %20 to avoid 403 errors
var encodedTerm = encodeURIComponent(searchTerm).replace(/%20/g, '+');
window.location.href = '/search/' + encodedTerm;
```

### 4. Search Toggle Fix Script

**File**: `app/webroot/js/search-toggle-fix.js`

**Purpose**: Universal fix for search toggle buttons across both main site and WordPress blog.

**Key Features**:
- **Conflict Resolution**: Removes `data-toggle` attributes to prevent `App.Navigation` interference
- **Element Cloning**: Removes existing event listeners by cloning elements
- **Multiple Detection Methods**: Finds search elements using various selectors
- **Responsive Behavior**: Different behavior for mobile vs desktop
- **Periodic Checking**: Handles dynamically added buttons

```javascript
// CRITICAL: Remove the data-toggle attribute to prevent App.Navigation from handling it
if (newSearchToggle.hasAttribute('data-toggle')) {
    console.log('[Search Toggle Fix] Removing data-toggle attribute');
    newSearchToggle.removeAttribute('data-toggle');
}
```

### 5. WordPress Blog Integration

**Search Template**: `app/webroot/blog/app/themes/bonvoyage/search.php`

**CSS Fixes**: `app/webroot/blog/app/themes/bonvoyage/assets/css/search-toggle-fix.css`

**Features**:
- **Responsive Design**: Different behavior for mobile (hidden by default) vs desktop (always visible)
- **Visual Consistency**: Matches main site styling and positioning
- **Toggle Functionality**: Proper show/hide behavior for mobile search

## Search Form Locations

### 1. Header Search Form
**Location**: `app/views/elements/chrome/page_header.ctp`
**ID**: `header-search-form`
**Input ID**: `header-search-input`

### 2. Sidebar Search Form
**Location**: `app/views/elements/chrome/search.ctp` (via `right_sidebar.ctp`)
**ID**: `search-form`
**Input ID**: `search-input`

### 3. Search Results Page Form
**Location**: Search results page sidebar
**Purpose**: "Search Again" functionality

## Error Prevention

### 1. 403 Error Prevention
- **Space Encoding**: Uses `+` instead of `%20` for spaces
- **URL Validation**: Ensures proper URL format before redirection
- **Input Sanitization**: Trims whitespace and validates search terms

### 2. JavaScript Error Prevention
- **Element Existence Checks**: Verifies elements exist before manipulation
- **Event Handler Isolation**: Prevents conflicts with other scripts
- **Graceful Degradation**: Falls back to standard form submission if JavaScript fails

### 3. Cross-Browser Compatibility
- **Event Listener Support**: Uses modern event handling with fallbacks
- **CSS Compatibility**: Uses vendor prefixes where necessary
- **Mobile Optimization**: Responsive design for various screen sizes

## Testing and Verification

### ✅ Search Functionality Tests

1. **Basic Search**: Search terms without spaces work correctly
2. **Spaced Terms**: Search terms with spaces use `+` encoding and work without 403 errors
3. **Special Characters**: Search terms with special characters are properly encoded
4. **Mobile Toggle**: Search toggle button works on mobile devices
5. **Desktop Display**: Search form always visible on desktop
6. **WordPress Integration**: Search works consistently on WordPress blog
7. **Clean URLs**: Search results use clean URL format (`/search/term`)
8. **Fallback Support**: Query string format works as fallback (`/search?search=term`)

### ✅ Cross-Platform Consistency

1. **Main Site**: All search forms work consistently
2. **WordPress Blog**: Search functionality matches main site behavior
3. **Mobile Responsive**: Proper behavior across all device sizes
4. **Browser Compatibility**: Works across modern browsers

## Maintenance Notes

### Search Form Updates
When modifying search forms, ensure:
1. **Consistent Encoding**: All forms use the same space encoding logic (`+` not `%20`)
2. **Event Handler Priority**: Use capture phase for event listeners
3. **Element Cloning**: Remove existing handlers before adding new ones
4. **Mobile Considerations**: Test toggle behavior on mobile devices

### URL Routing Changes
If modifying search routes:
1. **Backward Compatibility**: Maintain support for existing URL formats
2. **Parameter Handling**: Update controller to handle new parameter patterns
3. **WordPress Integration**: Ensure blog search still works with route changes

### Server Configuration
Monitor for:
1. **403 Errors**: Check server logs for URL encoding issues
2. **Rewrite Rules**: Ensure clean URLs are properly handled
3. **Cache Settings**: Verify search results aren't inappropriately cached

## Comparison with Original 'Working' Branch

### Overview of Changes

The search functionality has undergone significant improvements from the original 'working' branch to the current 'main' branch. This section details the specific differences and enhancements made.

### 1. Search Controller Changes

#### Original 'Working' Branch Behavior
```php
function results() {
    if(!empty($_GET['search'])) {
      $this->redirect('/search/'.$_GET['search']);
      exit;
    }

    // ... other code ...

    if (isset($this->params['term'])) {
      $term = str_replace('%20', ' ', $this->params['term']);
    }
}
```

**Issues with Original Implementation:**
- **Forced Redirects**: Automatically redirected GET parameter searches to clean URLs
- **Limited Encoding Support**: Only handled `%20` encoding, not `+` encoding
- **No Fallback**: No support for query string format as fallback
- **Auth Component Assumptions**: Assumed `allowedActions` was always an array

#### Current 'Main' Branch Improvements
```php
function results() {
    // Get search term from either URL parameter or GET parameter
    if (isset($this->params['term'])) {
      // Handle both + and %20 encoding for spaces
      $term = str_replace(array('+', '%20'), ' ', $this->params['term']);
    } elseif (!empty($_GET['search'])) {
      $term = $_GET['search'];
    }
}
```

**Improvements Made:**
- **Dual Parameter Support**: Accepts both URL parameters and GET parameters
- **Enhanced Encoding**: Handles both `+` and `%20` encoding for spaces
- **No Forced Redirects**: Allows both URL formats to work
- **Robust Auth Handling**: Checks if Auth component exists and initializes properly

### 2. Search Form Template Changes

#### Original 'Working' Branch Template
```html
<button class="search-toggle" data-toggle="search"></button>

<form class="primary-search__form" action="/search" method="get">
    <fieldset>
        <button>Submit</button>
        <div class="primary-search__input">
            <input type="text" name="search" id="search" placeholder="Search">
        </div>
        <input type="submit">
    </fieldset>
</form>
```

**Issues with Original Template:**
- **Empty Toggle Button**: No visual indicator (icon) in search toggle
- **No JavaScript Handling**: Relied on default form submission
- **Standard Form Behavior**: Used query string format (`?search=term`)
- **No Event Isolation**: Susceptible to conflicts with other scripts

#### Current 'Main' Branch Template
```html
<button class="search-toggle" data-toggle="search">
    <img src="/img/site/icons/search.svg" alt="Search">
</button>

<form class="primary-search__form" action="/search" method="get" id="search-form">
    <!-- Enhanced form with JavaScript handling -->
</form>

<script>
// Custom event handling with conflict prevention
var encodedTerm = encodeURIComponent(searchTerm).replace(/%20/g, '+');
window.location.href = '/search/' + encodedTerm;
</script>
```

**Improvements Made:**
- **Visual Search Icon**: Added search icon to toggle button
- **Clean URL Generation**: JavaScript converts to clean URL format
- **Event Isolation**: Clones elements to remove conflicting event listeners
- **Priority Event Handling**: Uses capture phase for higher priority
- **Space Encoding Fix**: Explicitly converts `%20` to `+` to prevent 403 errors

### 3. Search Toggle Fix Script

#### Original 'Working' Branch
- **No dedicated fix script**: The `search-toggle-fix.js` file did not exist
- **Navigation conflicts**: Search toggle buttons with images caused conflicts
- **Inconsistent behavior**: Different behavior across main site and blog

#### Current 'Main' Branch
- **New dedicated script**: `app/webroot/js/search-toggle-fix.js` (143 lines)
- **Universal fix**: Works across both main site and WordPress blog
- **Conflict resolution**: Removes `data-toggle` attributes to prevent conflicts
- **Responsive behavior**: Different handling for mobile vs desktop
- **Periodic checking**: Handles dynamically added buttons

### 4. Page Header Integration

#### Original 'Working' Branch
```php
<header class="page-header">
    <div class="page-header__inner">
        <a class="page-header__logo" href="/">USA &amp; Canada Holidays - Bon Voyage</a>
        <button class="nav-toggle"><span data-toggle="navigation"></span></button>
        <div id="navigation" class="nav-wrapper">
            <?php echo $this->element('chrome/secondary_nav'); ?>
            <?php echo $this->element('chrome/primary_nav'); ?>
        </div>
        <?php echo $this->element('chrome/search', array('heading' => false, 'modifier' => 'page-header')); ?>
    </div>
</header>
```

**Characteristics of Original:**
- **Simple structure**: Basic header with navigation and search element
- **Element-based search**: Used separate search element
- **No inline JavaScript**: Relied on external scripts

#### Current 'Main' Branch
```php
<header class="page-header">
    <!-- Comprehensive navigation structure with mega menus -->
    <!-- Inline search form with custom JavaScript -->
    <!-- Mobile menu integration -->
    <!-- Search toggle with icon -->
</header>
```

**Major Enhancements:**
- **Integrated search form**: Search form directly embedded in header
- **Inline JavaScript**: Custom search handling directly in template
- **Mobile menu integration**: Complete mobile navigation structure
- **Mega menu support**: Desktop dropdown menus
- **Visual improvements**: Search icon and better responsive design

### 5. WordPress Blog Integration

#### Original 'Working' Branch
- **Limited integration**: Basic WordPress search functionality
- **No cross-platform consistency**: Different behavior between main site and blog
- **No shared styling**: Separate CSS implementations

#### Current 'Main' Branch
- **Comprehensive integration**: Shared search toggle fix script
- **Consistent styling**: `search-toggle-fix.css` for visual consistency
- **Cross-platform compatibility**: Same behavior on both platforms
- **Responsive design**: Proper mobile/desktop handling

### 6. Error Handling and Robustness

#### Original 'Working' Branch Issues
- **403 Errors**: `%20` encoding caused server errors
- **Auth Component Errors**: Assumed `allowedActions` was always an array
- **JavaScript Conflicts**: No protection against conflicting scripts
- **Limited URL Support**: Only supported clean URLs properly

#### Current 'Main' Branch Solutions
- **403 Error Prevention**: Uses `+` encoding instead of `%20`
- **Robust Auth Handling**: Checks component existence and initializes safely
- **Conflict Prevention**: Element cloning and event isolation
- **Dual URL Support**: Works with both clean URLs and query strings

### 7. Performance and User Experience

#### Original 'Working' Branch
- **Forced Redirects**: Extra HTTP requests for URL format conversion
- **Limited Feedback**: No console logging or error reporting
- **Basic Functionality**: Standard form submission behavior

#### Current 'Main' Branch
- **No Forced Redirects**: Accepts multiple URL formats
- **Comprehensive Logging**: Console logging for debugging
- **Enhanced UX**: Visual feedback, responsive behavior, better error handling
- **Progressive Enhancement**: Works with and without JavaScript

## Summary of Improvements

### Critical Fixes
1. **403 Error Resolution**: Changed space encoding from `%20` to `+`
2. **Search Toggle Conflicts**: New dedicated fix script prevents navigation conflicts
3. **Auth Component Safety**: Robust checking prevents undefined property errors
4. **Cross-Platform Consistency**: Unified behavior between main site and blog

### User Experience Enhancements
1. **Visual Improvements**: Search icon in toggle button
2. **Responsive Design**: Better mobile/desktop handling
3. **Clean URLs**: JavaScript-generated clean URLs without forced redirects
4. **Error Prevention**: Comprehensive error handling and logging

### Technical Improvements
1. **Event Isolation**: Prevents conflicts with other JavaScript
2. **Dual Parameter Support**: Accepts both URL and query parameters
3. **Enhanced Encoding**: Supports multiple space encoding formats
4. **Progressive Enhancement**: Graceful degradation when JavaScript unavailable

The evolution from the 'working' branch to the 'main' branch represents a comprehensive overhaul of the search functionality, addressing critical bugs, improving user experience, and ensuring cross-platform consistency.

## Future Enhancements

### Potential Improvements
1. **Search Suggestions**: Auto-complete functionality
2. **Search Analytics**: Track popular search terms
3. **Advanced Filtering**: Category-based search filters
4. **Search Highlighting**: Highlight search terms in results
5. **Search History**: Remember recent searches

### Performance Optimizations
1. **Search Indexing**: Implement full-text search indexing
2. **Result Caching**: Cache popular search results
3. **Lazy Loading**: Load search results progressively
4. **Search API**: Create dedicated search API endpoints

## Navigation Logic Fixes

### Issues Identified

Two critical navigation issues were discovered and fixed:

1. **Holiday Types dropdown empty on blog**: The megamenu Holiday Types dropdown was empty on the WordPress blog
2. **Search results page navigation broken**: On search results pages, none of the dropdowns worked, and the mobile menu only showed basic links (Blog, FAQs, Make an Enquiry)

### Root Causes

#### 1. Holiday Types Data Format Mismatch
**Problem**: The AppController was passing raw holiday types data from the Navigation component, but the megamenu template expected a specific format with `text` and `url` keys.

**Original AppController code**:
```php
$holidayTypes = $navigationData['holidayTypes']; // Raw format: [['HolidayType' => ['name' => '...', 'slug' => '...']]]
```

**Expected by megamenu template**:
```php
// Template expects: [['text' => '...', 'url' => '...']]
<a href="<?php echo h($type['url']); ?>" class="mega-menu__link" title="<?php echo h($type['text']); ?>">
```

#### 2. Missing Navigation Component in Search Controller
**Problem**: The SearchController didn't have the Navigation component loaded, so it couldn't access navigation data.

**Original SearchController parent**:
```php
class SiteSearchAppController extends AppController {
    // No components defined - inherits from AppController but doesn't get Navigation component
}
```

### Fixes Applied

#### 1. Holiday Types Data Mapping in AppController
**File**: `app/app_controller.php`

Added proper data transformation to map holiday types to the format expected by the megamenu template:

```php
// Map holiday types to the format expected by megamenu template
$holidayTypes = array();
if (!empty($navigationData['holidayTypes'])) {
    foreach ($navigationData['holidayTypes'] as $type) {
        $holidayTypes[] = array(
            'text' => $type['HolidayType']['name'],
            'url' => '/holidays/' . $type['HolidayType']['slug']
        );
    }
}
```

**Before**: Holiday Types dropdown was empty on blog because data format didn't match template expectations
**After**: Holiday Types dropdown properly populated with all holiday types and correct links

#### 2. Navigation Component Added to Search Controller
**File**: `app/plugins/site_search/site_search_app_controller.php`

Added the Navigation component to ensure search pages have access to navigation data:

```php
class SiteSearchAppController extends AppController {
    var $components = array('Navigation');
}
```

**Before**: Search results pages had empty dropdowns and minimal mobile menu
**After**: Search results pages have fully populated navigation dropdowns and complete mobile menu

### Technical Details

#### Data Flow for Navigation
1. **Navigation Component** (`app/controllers/components/navigation.php`) retrieves raw data from database
2. **AppController** (`app/app_controller.php`) transforms data for template consumption
3. **Templates** (`mega_menu_api.ctp`, `mmenu_api.ctp`) render navigation using transformed data
4. **Blog/Search Pages** load navigation via AJAX from `/megamenu` and `/mmenu` endpoints

#### Holiday Types Data Transformation
```php
// Raw data from Navigation Component:
[
    ['HolidayType' => ['name' => 'Adventure Holidays', 'slug' => 'adventure-holidays']],
    ['HolidayType' => ['name' => 'Family Holidays', 'slug' => 'family-holidays']]
]

// Transformed data for megamenu template:
[
    ['text' => 'Adventure Holidays', 'url' => '/holidays/adventure-holidays'],
    ['text' => 'Family Holidays', 'url' => '/holidays/family-holidays']
]
```

#### Search Controller Navigation Access
```php
// SearchController inheritance chain:
SearchController extends SiteSearchAppController extends AppController

// With Navigation component added:
SiteSearchAppController->components = ['Navigation']

// Result: SearchController now has access to:
$this->Navigation->getNavigationData()
```

### Testing Verification

#### ✅ Blog Holiday Types Dropdown
1. Navigate to WordPress blog
2. Hover over "Holiday Types" in desktop navigation
3. Verify dropdown shows all holiday types with correct links
4. Click on holiday type links to verify they work

#### ✅ Search Results Navigation
1. Perform a search on the main site
2. On search results page, verify all navigation dropdowns work:
   - USA destinations with sub-destinations
   - Canada destinations with sub-destinations
   - Holiday Types with all types
   - What's Hot with current spotlights
   - Holiday Info with page hierarchy
   - About Us with all pages
3. Test mobile menu on search results page
4. Verify all sections are populated with content

#### ✅ Cross-Platform Consistency
1. Compare navigation between main site, blog, and search results
2. Verify identical content and functionality across all platforms
3. Test both desktop dropdowns and mobile menu

### Impact

These fixes ensure that:
- **Blog navigation is fully functional** with all dropdowns properly populated
- **Search results pages have complete navigation** instead of minimal links
- **Data consistency** is maintained across all platforms
- **User experience** is seamless regardless of which part of the site they're on

The fixes address the core issue of navigation data not being properly formatted or accessible, ensuring that all parts of the site have access to the same comprehensive navigation structure.
